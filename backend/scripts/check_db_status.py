"""
检查数据库状态脚本
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.insert(0, backend_dir)

from app.core.database import SessionLocal
from app.models.customer_service import *
from app.models.user import User


def check_database_status():
    """检查数据库状态"""
    print("📊 检查数据库状态...")
    
    db = SessionLocal()
    try:
        # 检查用户
        users = db.query(User).all()
        print(f"\n👥 用户数量: {len(users)}")
        for user in users:
            print(f"   - {user.username} ({user.email}) - {'管理员' if user.is_superuser else '普通用户'}")
        
        # 检查产品分类
        categories = db.query(Category).all()
        print(f"\n📂 产品分类数量: {len(categories)}")
        for category in categories:
            print(f"   - {category.name}: {category.description}")
        
        # 检查产品
        products = db.query(Product).all()
        print(f"\n📦 产品数量: {len(products)}")
        for product in products:
            print(f"   - {product.name} (SKU: {product.sku}) - ¥{product.price}")
        
        # 检查订单
        orders = db.query(Order).all()
        print(f"\n🛒 订单数量: {len(orders)}")
        for order in orders:
            print(f"   - {order.order_number} - {order.status.value} - ¥{order.final_amount}")
        
        # 检查优惠券
        coupons = db.query(Coupon).all()
        print(f"\n🎫 优惠券数量: {len(coupons)}")
        for coupon in coupons:
            print(f"   - {coupon.code}: {coupon.name}")
        
        # 检查客服会话
        sessions = db.query(CustomerServiceSession).all()
        print(f"\n💬 客服会话数量: {len(sessions)}")
        for session in sessions:
            print(f"   - {session.session_id} - {session.status}")
        
        print(f"\n✅ 数据库状态检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        raise
    finally:
        db.close()


if __name__ == "__main__":
    check_database_status()
