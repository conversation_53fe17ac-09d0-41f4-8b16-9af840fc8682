"""
数据库初始化脚本
"""

import sys
import os
from decimal import Decimal
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.insert(0, backend_dir)

from app.core.database import SessionLocal, create_tables
from app.models.customer_service import *
from app.models.user import User
from app.core.security import get_password_hash


def init_database():
    """初始化数据库"""
    print("🗄️ 创建数据库表...")
    create_tables()
    
    db = SessionLocal()
    try:
        # 创建管理员用户
        print("👤 创建管理员用户...")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            is_active=True,
            is_superuser=True
        )
        db.add(admin_user)
        
        # 创建测试用户
        test_user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=get_password_hash("test123"),
            full_name="测试用户",
            is_active=True,
            is_vip=True,
            vip_level=1
        )
        db.add(test_user)
        
        db.flush()  # 获取用户ID
        
        # 创建产品分类
        print("📂 创建产品分类...")
        categories = [
            Category(name="电子产品", description="各类电子设备"),
            Category(name="服装鞋帽", description="时尚服饰"),
            Category(name="家居用品", description="家庭生活用品"),
            Category(name="图书音像", description="书籍和音像制品"),
        ]
        
        for category in categories:
            db.add(category)
        
        db.flush()
        
        # 创建测试产品
        print("📦 创建测试产品...")
        products = [
            Product(
                name="iPhone 15 Pro",
                model="A3102",
                sku="IPHONE15PRO128",
                description="苹果最新旗舰手机",
                specifications={"颜色": "深空黑色", "存储": "128GB", "屏幕": "6.1英寸"},
                price=Decimal("7999.00"),
                original_price=Decimal("8999.00"),
                material="钛金属",
                size_guide="长度: 146.6mm, 宽度: 70.6mm, 厚度: 8.25mm",
                images=["iphone15pro_1.jpg", "iphone15pro_2.jpg"],
                category_id=categories[0].id,
                is_featured=True
            ),
            Product(
                name="Nike Air Max 270",
                model="AH8050",
                sku="NIKE270BLACK42",
                description="经典气垫跑鞋",
                specifications={"颜色": "黑色", "尺码": "42", "材质": "网布+合成革"},
                price=Decimal("899.00"),
                original_price=Decimal("1299.00"),
                material="网布面料",
                size_guide="建议选择正常尺码",
                images=["nike270_1.jpg", "nike270_2.jpg"],
                category_id=categories[1].id,
                is_featured=True
            ),
        ]
        
        for product in products:
            db.add(product)
        
        db.flush()
        
        # 创建库存记录
        print("📊 创建库存记录...")
        for product in products:
            inventory = Inventory(
                product_id=product.id,
                stock_quantity=100,
                available_quantity=100,
                low_stock_threshold=10
            )
            db.add(inventory)
        
        # 创建促销活动
        print("🎉 创建促销活动...")
        promotion = Promotion(
            name="双十一大促",
            description="全场满减优惠",
            type=PromotionType.FULL_REDUCTION,
            rules={
                "thresholds": [
                    {"min_amount": 500, "discount_amount": 50},
                    {"min_amount": 1000, "discount_amount": 120},
                    {"min_amount": 2000, "discount_amount": 300}
                ]
            },
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(days=30),
            priority=1
        )
        db.add(promotion)
        db.flush()
        
        # 创建优惠券
        print("🎫 创建优惠券...")
        coupons = [
            Coupon(
                code="WELCOME10",
                name="新用户专享券",
                promotion_id=promotion.id,
                discount_type="percentage",
                discount_value=Decimal("10"),
                min_order_amount=Decimal("100"),
                max_discount_amount=Decimal("50"),
                usage_limit=1000,
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(days=30)
            ),
            Coupon(
                code="SAVE50",
                name="满500减50",
                promotion_id=promotion.id,
                discount_type="fixed_amount",
                discount_value=Decimal("50"),
                min_order_amount=Decimal("500"),
                usage_limit=500,
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(days=15)
            ),
        ]
        
        for coupon in coupons:
            db.add(coupon)
        
        db.flush()
        
        # 给测试用户发放优惠券
        for coupon in coupons:
            user_coupon = UserCoupon(
                user_id=test_user.id,
                coupon_id=coupon.id
            )
            db.add(user_coupon)
        
        # 创建测试订单
        print("🛒 创建测试订单...")
        test_order = Order(
            order_number="ORD20241201001",
            user_id=test_user.id,
            status=OrderStatus.DELIVERED,
            total_amount=Decimal("7999.00"),
            final_amount=Decimal("7999.00"),
            shipping_address={
                "name": "测试用户",
                "phone": "13800138000",
                "address": "北京市朝阳区测试街道123号"
            },
            shipping_method="顺丰快递",
            payment_method="微信支付"
        )
        db.add(test_order)
        db.flush()
        
        # 创建订单商品
        order_item = OrderItem(
            order_id=test_order.id,
            product_id=products[0].id,
            quantity=1,
            unit_price=Decimal("7999.00"),
            total_price=Decimal("7999.00"),
            specifications={"颜色": "深空黑色", "存储": "128GB"}
        )
        db.add(order_item)
        
        # 创建物流跟踪
        logistics = LogisticsTracking(
            order_id=test_order.id,
            tracking_number="SF1234567890",
            logistics_company="顺丰速运",
            logistics_company_code="SF",
            current_status="已签收",
            tracking_info=[
                {"time": "2024-12-01 10:00:00", "status": "已下单", "location": "北京"},
                {"time": "2024-12-01 14:00:00", "status": "已发货", "location": "北京分拣中心"},
                {"time": "2024-12-02 09:00:00", "status": "运输中", "location": "北京-朝阳区"},
                {"time": "2024-12-02 15:30:00", "status": "已签收", "location": "朝阳区测试街道"}
            ]
        )
        db.add(logistics)
        
        db.commit()
        print("✅ 数据库初始化完成！")
        
        print("\n📋 测试账号信息:")
        print("管理员账号: admin / admin123")
        print("测试用户账号: testuser / test123")
        print("测试订单号: ORD20241201001")
        print("测试优惠券: WELCOME10, SAVE50")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    init_database()
